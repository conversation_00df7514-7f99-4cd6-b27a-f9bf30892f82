/* Crop Feature Styles */

/* Crop Icon */
.crop-icon-container {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
}

.crop-icon-container:hover {
  transform: scale(1.1);
  background-color: var(--accent-color);
  opacity: 1;
}

.crop-icon {
  color: var(--text-color);
  font-size: 1.2rem;
}

/* Crop Modal */
.crop-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  overflow: auto;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.crop-modal-content {
  position: relative;
  background-color: var(--secondary-color);
  background-image: var(--modern-gradient);
  margin: 5% auto;
  padding: 30px;
  width: 80%;
  max-width: 900px;
  border-radius: 15px;
  box-shadow: var(--card-shadow), var(--subtle-glow);
  border: var(--modern-border);
  animation: slideUp 0.4s ease;
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.crop-modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 20%, var(--card-highlight) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, var(--card-highlight) 0%, transparent 50%);
  opacity: 0.5;
  z-index: 0;
  pointer-events: none;
  border-radius: 15px;
}

.crop-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.crop-modal-title {
  font-size: 1.8rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.crop-modal-title i {
  color: var(--highlight-color);
}

.crop-close {
  font-size: 1.5rem;
  color: var(--accent-light);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.crop-close:hover {
  color: var(--highlight-color);
  background-color: rgba(255, 255, 255, 0.1);
  transform: rotate(90deg);
}

.crop-container {
  position: relative;
  width: 100%;
  height: 400px;
  background-color: #000;
  overflow: hidden;
  margin-bottom: 20px;
  border-radius: 10px;
  border: 1px solid var(--border-color);
}

.crop-img {
  display: block;
  max-width: 100%;
  max-height: 100%;
}

.crop-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.crop-control-group {
  flex: 1;
  min-width: 200px;
}

.crop-control-label {
  display: block;
  margin-bottom: 10px;
  color: var(--text-color);
  font-weight: 500;
  font-size: 1rem;
}

.crop-aspect-ratio {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.crop-aspect-btn {
  padding: 8px 15px;
  background-color: var(--accent-color);
  color: var(--text-color);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.crop-aspect-btn:hover {
  background-color: var(--accent-light);
  transform: translateY(-2px);
}

.crop-aspect-btn.active {
  background-color: var(--highlight-color);
  color: var(--primary-color);
  font-weight: 600;
}

.crop-zoom-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.crop-zoom-btn {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  background-color: var(--accent-color);
  color: var(--text-color);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.4rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.crop-zoom-btn:hover {
  background-color: var(--accent-light);
  transform: scale(1.1);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
}

.crop-zoom-btn:active {
  transform: scale(0.95);
  background-color: var(--highlight-color);
  color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

/* Add ripple effect */
.crop-zoom-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  transition: transform 0.4s ease-out, opacity 0.4s ease-out;
}

.crop-zoom-btn:active::after {
  transform: translate(-50%, -50%) scale(2);
  opacity: 0;
  transition: 0s;
}

/* Active state for zoom buttons */
.crop-zoom-btn.active {
  background-color: var(--highlight-color);
  color: var(--primary-color);
  transform: scale(0.95);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  animation: pulse 0.3s ease-out;
}

@keyframes pulse {
  0% { transform: scale(0.95); }
  50% { transform: scale(1.05); }
  100% { transform: scale(0.95); }
}

/* Ripple effect */
.ripple-effect {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

.crop-zoom-slider {
  flex: 1;
  height: 8px;
  background-color: var(--accent-color);
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  margin: 0 10px;
  overflow: visible;
}

.crop-zoom-slider::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.2), rgba(255, 255, 255, 0.2));
  border-radius: 4px;
}

/* Zoom level markers */
.crop-zoom-slider::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2px;
  height: 10px;
  background-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-50%);
}

.crop-zoom-slider-thumb {
  width: 26px;
  height: 26px;
  background-color: var(--highlight-color);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%; /* Default position in the middle */
  transform: translate(-50%, -50%);
  cursor: grab;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.5);
  transition: transform 0.15s ease, box-shadow 0.2s ease, left 0.2s ease-out, background-color 0.2s ease;
  z-index: 5;
  border: 3px solid rgba(255, 255, 255, 0.9);
}

.crop-zoom-slider-thumb:hover {
  transform: translate(-50%, -50%) scale(1.15);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5);
  background-color: #ffffff;
}

.crop-zoom-slider-thumb:active {
  cursor: grabbing;
  transform: translate(-50%, -50%) scale(1.1);
}

/* Animation for zoom buttons interaction */
.crop-zoom-slider-thumb.zooming {
  transform: translate(-50%, -50%) scale(1.3);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.7);
  background-color: #ffffff;
  border-color: var(--highlight-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, left 0.25s cubic-bezier(0.25, 1, 0.5, 1);
  animation: pulse 0.3s ease-out;
}

.crop-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
  position: relative;
  z-index: 1;
}

.crop-action-btn {
  padding: 12px 25px;
  border-radius: 10px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
}

.crop-cancel-btn {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--accent-color);
}

.crop-cancel-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.crop-apply-btn {
  background-color: var(--highlight-color);
  color: var(--primary-color);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  font-weight: 600;
}

.crop-apply-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
  background-color: #ffffff;
}

/* Responsive styles */
@media (max-width: 768px) {
  .crop-modal-content {
    width: 95%;
    padding: 20px;
    margin: 10% auto;
  }

  .crop-container {
    height: 300px;
  }

  .crop-controls {
    flex-direction: column;
    gap: 15px;
  }

  .crop-actions {
    flex-direction: column;
  }

  .crop-action-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .crop-container {
    height: 250px;
  }

  .crop-modal-title {
    font-size: 1.4rem;
  }

  .crop-aspect-btn {
    padding: 6px 10px;
    font-size: 0.8rem;
  }
}