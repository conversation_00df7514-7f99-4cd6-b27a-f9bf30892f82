/* Blog Styles for ImgNinja */

/* Blog Listing Page */
.blog-filters {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
  animation: fadeIn 0.8s ease;
}

.search-container {
  display: flex;
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
}

.search-container input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  background-color: var(--secondary-color);
  color: var(--text-color);
  border-radius: 8px 0 0 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.search-container input:focus {
  outline: none;
  border-color: var(--highlight-color);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.search-container button {
  background-color: var(--highlight-color);
  color: var(--primary-color);
  border: none;
  padding: 0 20px;
  border-radius: 0 8px 8px 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-container button:hover {
  background-color: var(--accent-light);
  color: var(--text-color);
}

.category-filters {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

.category-filter {
  padding: 8px 15px;
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.category-filter:hover {
  background-color: var(--accent-color);
}

.category-filter.active {
  background-color: var(--highlight-color);
  color: var(--primary-color);
  border-color: var(--highlight-color);
}

/* Featured Post */
.featured-post {
  display: flex;
  flex-direction: column;
  background-color: var(--secondary-color);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 60px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  animation: fadeIn 1s ease;
}

.featured-post-image {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
}

.featured-post-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.featured-post:hover .featured-post-image img {
  transform: scale(1.05);
}

.featured-badge {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: var(--highlight-color);
  color: var(--primary-color);
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  z-index: 1;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.featured-post-content {
  padding: 30px;
}

.featured-post-content h2 {
  margin: 10px 0 15px;
  font-size: 1.8rem;
  line-height: 1.3;
}

.featured-excerpt {
  margin-bottom: 20px;
  color: var(--text-color);
  opacity: 0.9;
  line-height: 1.6;
}

.read-more-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: var(--highlight-color);
  color: var(--primary-color);
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  margin-top: 15px;
}

.read-more-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Blog Grid */
.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}

.blog-card {
  background-color: var(--secondary-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: fadeIn 1s ease;
  animation-fill-mode: both;
}

.blog-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
}

.blog-card:nth-child(1) { animation-delay: 0.1s; }
.blog-card:nth-child(2) { animation-delay: 0.2s; }
.blog-card:nth-child(3) { animation-delay: 0.3s; }
.blog-card:nth-child(4) { animation-delay: 0.4s; }
.blog-card:nth-child(5) { animation-delay: 0.5s; }
.blog-card:nth-child(6) { animation-delay: 0.6s; }

.blog-card-image {
  display: block;
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
}

.blog-card-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.1);
}

.blog-card-content {
  padding: 20px;
}

.blog-category {
  display: inline-block;
  padding: 5px 10px;
  background-color: var(--accent-color);
  color: var(--text-color);
  border-radius: 15px;
  font-size: 0.8rem;
  margin-bottom: 10px;
}

.blog-card-content h3 {
  margin: 10px 0;
  font-size: 1.3rem;
  line-height: 1.4;
}

.blog-card-content h3 a {
  color: var(--text-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.blog-card-content h3 a:hover {
  color: var(--highlight-color);
}

.blog-excerpt {
  margin-bottom: 15px;
  color: var(--text-color);
  opacity: 0.8;
  font-size: 0.9rem;
  line-height: 1.5;
}

.blog-meta {
  display: flex;
  gap: 15px;
  font-size: 0.85rem;
  color: var(--text-color);
  opacity: 0.7;
}

.blog-date, .blog-reading-time {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 50px 0;
}

.pagination-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pagination-btn:hover {
  background-color: var(--accent-color);
}

.pagination-btn.active {
  background-color: var(--highlight-color);
  color: var(--primary-color);
  border-color: var(--highlight-color);
}

.pagination-btn.next {
  width: auto;
  padding: 0 15px;
}

/* Newsletter Section */
.newsletter-section {
  background-color: var(--secondary-color);
  border-radius: 12px;
  padding: 40px;
  margin: 60px 0;
  text-align: center;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  background-image: var(--card-gradient);
}

.newsletter-content {
  max-width: 600px;
  margin: 0 auto;
}

.newsletter-content h2 {
  margin-bottom: 15px;
  font-size: 1.8rem;
}

.newsletter-content p {
  margin-bottom: 25px;
  opacity: 0.9;
}

.newsletter-form {
  display: flex;
  max-width: 500px;
  margin: 0 auto;
}

.newsletter-form input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  background-color: var(--primary-color);
  color: var(--text-color);
  border-radius: 8px 0 0 8px;
  font-size: 1rem;
}

.newsletter-form input:focus {
  outline: none;
  border-color: var(--highlight-color);
}

.newsletter-form button {
  background-color: var(--highlight-color);
  color: var(--primary-color);
  border: none;
  padding: 0 25px;
  border-radius: 0 8px 8px 0;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.newsletter-form button:hover {
  background-color: var(--accent-light);
  color: var(--text-color);
}

.newsletter-disclaimer {
  margin-top: 15px;
  font-size: 0.8rem;
  opacity: 0.7;
}

/* Blog Post Page Styles */
.blog-header {
  text-align: center;
  margin-bottom: 30px;
  animation: fadeIn 0.8s ease;
}

.blog-title {
  font-size: 2.5rem;
  margin: 15px 0;
  line-height: 1.3;
}

.blog-featured-image {
  margin-bottom: 40px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  animation: fadeIn 1s ease;
}

.blog-featured-image img {
  width: 100%;
  height: auto;
  display: block;
}

.blog-content {
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.8;
  animation: fadeIn 1.2s ease;
}

.blog-intro {
  font-size: 1.2rem;
  color: var(--text-color);
  margin-bottom: 30px;
  line-height: 1.6;
}

.blog-content h2 {
  margin: 40px 0 20px;
  font-size: 1.8rem;
}

.blog-content h3 {
  margin: 30px 0 15px;
  font-size: 1.4rem;
}

.blog-content p {
  margin-bottom: 20px;
}

.blog-content ul, .blog-content ol {
  margin: 20px 0;
  padding-left: 25px;
}

.blog-content li {
  margin-bottom: 10px;
}

.blog-content a {
  color: var(--primary-color);
  text-decoration: none;
  border-bottom: 1px dotted var(--highlight-color);
  transition: border-bottom 0.3s ease;
}

.blog-content a:hover {
  border-bottom: 1px solid var(--highlight-color);
}

.blog-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 40px 0;
  align-items: center;
}

.tag-label {
  font-weight: 600;
}

.blog-tag {
  display: inline-block;
  padding: 5px 12px;
  background-color: var(--accent-color);
  color: var(--text-color);
  border-radius: 20px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.blog-tag:hover {
  background-color: var(--highlight-color);
  color: var(--primary-color);
  transform: translateY(-2px);
}

.blog-author {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 40px 0;
  padding: 25px;
  background-color: var(--secondary-color);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.author-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info h3 {
  margin: 0 0 10px;
}

.author-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.95rem;
}

.share-post {
  margin: 40px 0;
  text-align: center;
}

.share-post h3 {
  margin-bottom: 15px;
}

.social-share {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.social-share a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background-color: var(--accent-color);
  color: var(--text-color);
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.social-share a:hover {
  background-color: var(--highlight-color);
  color: var(--primary-color);
  transform: translateY(-3px);
}

.related-posts {
  margin: 60px 0;
}

.related-posts h2 {
  text-align: center;
  margin-bottom: 30px;
}

.related-posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 25px;
}

.related-post-card {
  background-color: var(--secondary-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  transition: transform 0.3s ease;
}

.related-post-card:hover {
  transform: translateY(-5px);
}

.related-post-card img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.related-post-card:hover img {
  transform: scale(1.1);
}

.related-post-card h3 {
  padding: 15px;
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.4;
}

.related-post-card a {
  color: var(--text-color);
  text-decoration: none;
}

.related-post-card .blog-date {
  padding: 0 15px 15px;
  font-size: 0.8rem;
  opacity: 0.7;
}

.cta-section {
  text-align: center;
  background-color: var(--secondary-color);
  padding: 40px;
  border-radius: 12px;
  margin: 60px 0 20px;
  border: 1px solid var(--border-color);
  background-image: var(--card-gradient);
}

.cta-section h2 {
  margin-bottom: 15px;
}

.cta-section p {
  margin-bottom: 25px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-button {
  display: inline-block;
  padding: 12px 25px;
  background-color: var(--highlight-color);
  color: var(--primary-color);
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Code blocks */
pre {
  background-color: var(--primary-color);
  padding: 20px;
  border-radius: 8px;
  overflow-x: auto;
  position: relative;
  margin: 25px 0;
  border: 1px solid var(--border-color);
}

code {
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
}

.copy-code-button {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 5px 10px;
  background-color: var(--accent-color);
  color: var(--text-color);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.copy-code-button:hover {
  background-color: var(--highlight-color);
  color: var(--primary-color);
}

/* Tables */
.comparison-table {
  overflow-x: auto;
  margin: 30px 0;
}

table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid var(--border-color);
}

th, td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background-color: var(--accent-color);
  font-weight: 600;
}

tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.03);
}

/* Format comparison cards */
.format-comparison {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.format-card {
  background-color: var(--secondary-color);
  padding: 20px;
  border-radius: 10px;
  border: 1px solid var(--border-color);
}

.format-card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--highlight-color);
}

.format-card p {
  margin: 10px 0;
}

/* Tip box */
.tip-box {
  background-color: rgba(255, 255, 255, 0.05);
  border-left: 4px solid var(--highlight-color);
  padding: 20px;
  margin: 30px 0;
  border-radius: 0 8px 8px 0;
}

.tip-box h3 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 0;
  color: var(--highlight-color);
}

.tip-box p {
  margin-bottom: 0;
}

/* Browser support section */
.browser-support {
  background-color: var(--secondary-color);
  padding: 20px;
  border-radius: 10px;
  margin: 30px 0;
  border: 1px solid var(--border-color);
}

.browser-support ul {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive styles */
@media (max-width: 768px) {
  .featured-post {
    flex-direction: column;
  }

  .blog-title {
    font-size: 1.8rem;
  }

  .blog-grid {
    grid-template-columns: 1fr;
  }

  .related-posts-grid {
    grid-template-columns: 1fr;
  }

  .newsletter-form {
    flex-direction: column;
  }

  .newsletter-form input {
    border-radius: 8px;
    margin-bottom: 10px;
  }

  .newsletter-form button {
    border-radius: 8px;
    padding: 12px;
  }

  .blog-author {
    flex-direction: column;
    text-align: center;
  }

  .format-comparison {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .category-filters {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 10px;
    justify-content: flex-start;
  }

  .category-filter {
    white-space: nowrap;
  }

  .featured-post-content {
    padding: 20px;
  }

  .featured-post-content h2 {
    font-size: 1.5rem;
  }

  .blog-card-content h3 {
    font-size: 1.2rem;
  }

  .social-share {
    flex-wrap: wrap;
  }
}
