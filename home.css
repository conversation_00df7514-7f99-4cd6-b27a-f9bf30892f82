/* Home Page Specific Styles for ImgNinja Landing Page */

/* Home Container */
.home-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Hero Landing Section */
.hero-landing {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 20px 40px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    var(--primary-color) 0%, 
    var(--secondary-color) 25%, 
    var(--accent-color) 50%, 
    var(--secondary-color) 75%, 
    var(--primary-color) 100%);
  opacity: 0.8;
  z-index: -2;
}

.hero-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.02) 0%, transparent 50%);
  z-index: -1;
}

.hero-content {
  text-align: center;
  max-width: 800px;
  z-index: 1;
}

.hero-logo {
  margin-bottom: 30px;
  animation: fadeInUp 1s ease-out;
}

.hero-logo-img {
  max-width: 300px;
  height: auto;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.3));
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--highlight-color);
  animation: fadeInUp 1s ease-out 0.2s both;
}

.brand-highlight {
  background: linear-gradient(45deg, var(--highlight-color), var(--accent-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 500;
  color: var(--accent-light);
  margin-bottom: 20px;
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-description {
  font-size: 1.1rem;
  color: var(--text-color);
  line-height: 1.6;
  margin-bottom: 40px;
  animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-cta {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 0.8s both;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all var(--transition-speed) ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.cta-button.primary {
  background: linear-gradient(45deg, var(--highlight-color), var(--accent-light));
  color: var(--primary-color);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
}

.cta-button.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(255, 255, 255, 0.3);
}

.cta-button.secondary {
  background: transparent;
  color: var(--highlight-color);
  border-color: var(--highlight-color);
}

.cta-button.secondary:hover {
  background: var(--highlight-color);
  color: var(--primary-color);
  transform: translateY(-3px);
}

.cta-button.large {
  padding: 18px 35px;
  font-size: 1.1rem;
}

/* Features Section */
.features-section {
  padding: 80px 20px;
  background: var(--secondary-color);
  background-image: var(--card-gradient);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--highlight-color);
  margin-bottom: 15px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-color);
  opacity: 0.8;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: var(--primary-color);
  background-image: var(--card-gradient);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid var(--border-color);
  box-shadow: var(--card-shadow);
  transition: all var(--transition-speed) ease;
  position: relative;
  overflow: hidden;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
}

.feature-card.live {
  border-color: var(--highlight-color);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.1);
}

.feature-card.live:hover {
  box-shadow: 0 20px 50px rgba(255, 255, 255, 0.2);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, var(--accent-color), var(--accent-light));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  font-size: 2rem;
  color: var(--highlight-color);
}

.feature-card.live .feature-icon {
  background: linear-gradient(45deg, var(--highlight-color), var(--accent-light));
  color: var(--primary-color);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--highlight-color);
  margin-bottom: 15px;
}

.feature-description {
  color: var(--text-color);
  line-height: 1.6;
  margin-bottom: 20px;
}

.feature-highlights {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 25px;
}

.highlight-tag {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  background: var(--accent-color);
  color: var(--text-color);
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.feature-card.live .highlight-tag {
  background: rgba(255, 255, 255, 0.1);
  color: var(--highlight-color);
}

.feature-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 25px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  transition: all var(--transition-speed) ease;
  border: none;
  cursor: pointer;
  font-size: 0.95rem;
}

.live-button {
  background: linear-gradient(45deg, var(--highlight-color), var(--accent-light));
  color: var(--primary-color);
}

.live-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 255, 255, 0.3);
}

.upcoming-button {
  background: var(--accent-color);
  color: var(--text-color);
  cursor: not-allowed;
  opacity: 0.7;
}

.live-badge, .upcoming-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
}

.live-badge {
  background: linear-gradient(45deg, #00ff00, #32cd32);
  color: var(--primary-color);
}

.live-badge i {
  animation: pulse 2s infinite;
}

.upcoming-badge {
  background: var(--accent-color);
  color: var(--text-color);
}

/* Why Choose Section */
.why-choose-section {
  padding: 80px 20px;
  background: var(--primary-color);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.benefit-card {
  background: var(--secondary-color);
  background-image: var(--card-gradient);
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: all var(--transition-speed) ease;
}

.benefit-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow);
}

.benefit-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(45deg, var(--accent-color), var(--accent-light));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 1.8rem;
  color: var(--highlight-color);
}

.benefit-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--highlight-color);
  margin-bottom: 15px;
}

.benefit-description {
  color: var(--text-color);
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: 80px 20px;
  background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--highlight-color);
  margin-bottom: 20px;
}

.cta-description {
  font-size: 1.2rem;
  color: var(--text-color);
  margin-bottom: 40px;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  .hero-cta {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-button {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .cta-title {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .hero-landing {
    padding: 60px 15px 30px;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-logo-img {
    max-width: 250px;
  }
  
  .feature-card, .benefit-card {
    padding: 20px;
  }
  
  .features-section, .why-choose-section, .cta-section {
    padding: 60px 15px;
  }
}
